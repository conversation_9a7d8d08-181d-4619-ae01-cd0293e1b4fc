package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SkuStockWarningContent {
    /**
     * 业务预警类型
     */
    private int type;
    /**
     * 预警时间
     */
    private Date time;
    /**
     * 预警发送人
     */
    private String sender="系统";

    /**
     * 预警商品SKU编码
     */
    private String productSkuCode;
    /**
     * 预警商品编码
     */
    private String productCode;
    /**
     * 自提库存
     */
    private Integer pickUpStock;
    /**
     * 代发库存
     */
    private Integer dropShippingStock;
}
