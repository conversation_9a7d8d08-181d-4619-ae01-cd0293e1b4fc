package com.zsmall.system.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.bo.announcement.AnnouncementBo;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementMessageVo;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo;

import java.util.Collection;
import java.util.List;

/**
 * 公告Service接口
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
public interface IAnnouncementService {

    /**
     * 查询公告详情
     * @param id 公告ID
     * @return 公告详情
     */
    AnnouncementVo queryById(Long id);

    /**
     * 分页查询公告列表（管理员端）
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<AnnouncementVo> queryPageList(AnnouncementBo bo, PageQuery pageQuery);

    /**
     * 新增公告
     * @param bo 公告信息
     * @return 是否成功
     */
    Boolean insertByBo(AnnouncementBo bo);

    /**
     * 修改公告
     * @param bo 公告信息
     * @return 是否成功
     */
    Boolean updateByBo(AnnouncementBo bo);

    /**
     * 删除公告（管理员删除，所有租户不可见）
     * @param ids 公告ID集合
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 删除公告（租户级删除，仅对指定租户不可见）
     * @param ids 公告ID集合
     * @param tenantId 租户ID
     * @return 是否成功
     */
    Boolean deleteForTenant(Collection<Long> ids, String tenantId);

    /**
     * 更新公告状态（启用/停用）
     * @param id 公告ID
     * @param status 状态
     * @return 是否成功
     */
    Boolean updateStatus(Long id, Integer status);



    /**
     * 查询消息列表（分页）
     * @param tenantId 租户ID
     * @param tenantType 租户类型
     * @param isRead 是否已读（可选）
     * @param pageQuery 分页参数
     * @return 消息列表
     */
    TableDataInfo<AnnouncementMessageVo> queryMessageList(String tenantId, String tenantType, Integer isRead, PageQuery pageQuery);


    /**
     * 查询消息列表（不分页）
     * @param tenantId 租户ID
     * @param tenantType 租户类型
     * @param isRead 是否已读（可选）
     * @return 消息列表
     */
    List<AnnouncementMessageVo> queryMessageList(String tenantId, String tenantType, Integer isRead);


    /**
     * 批量标记已读
     * @param announcementIds 公告ID列表
     * @param tenantId 租户ID
     * @return 是否成功
     */
    Boolean batchMarkRead(List<Long> announcementIds, String tenantId);

    /**
     * 批量标记未读
     * @param announcementIds 公告ID列表
     * @param tenantId 租户ID
     * @return 是否成功
     */
    Boolean batchMarkUnread(List<Long> announcementIds, String tenantId);

    /**
     * 处理公告过期（由消息队列调用）
     * @param announcementId 公告ID
     */
    void handleAnnouncementExpire(Long announcementId);

    /**
     * 弹窗查询公告列表
     * @param tenantId 租户ID
     * @return 公告列表
     */
    List<AnnouncementVo> popWindowSearch(String tenantId);

    /**
     * 批量标记弹窗已读
     * @param announcementIds 公告ID列表
     * @param tenantId 租户ID
     */
    void batchPopWindows( List<Long> announcementIds, String tenantId);

    Long countUnreadMessages(String tenantId);
}
